import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'core/config/environment.dart';
import 'core/debug/debug_console_monitor.dart';
import 'core/error_handling/sentry_error_handler.dart';

import 'core/providers/analytics_provider.dart';
import 'core/providers/app_settings_selectors.dart';
import 'core/providers/master_location_provider.dart';
import 'core/providers/optimized_provider_loader.dart';
import 'core/providers/provider_performance_monitor.dart';
import 'core/providers/unified_monitoring_provider.dart';
import 'core/services/deep_link_service.dart';
import 'core/services/navigation_state_manager.dart';
import 'core/services/service_registry.dart';
import 'core/services/unified_app_lifecycle_manager.dart';
import 'core/services/unified_initialization_service.dart';
import 'core/settings/performance/performance_settings_provider.dart';
import 'core/storage/hive_service.dart';
import 'core/sync/services/background_task_callback_dispatcher.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/logger.dart';
import 'core/utils/safe_state_updater.dart';
import 'core/utils/system_navigation_handler.dart';
import 'features/auth/presentation/pages/forgot_password_page.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/auth/presentation/pages/signup_page.dart';
import 'features/debug/gesture_debug_page.dart';
import 'features/debug/performance_dashboard.dart';
import 'features/duas/presentation/pages/duas_page.dart';
import 'features/duas/presentation/widgets/duas_layout.dart';
import 'features/events/presentation/pages/events_page.dart';
import 'features/favorites/presentation/pages/favorites_page.dart';
import 'features/home/<USER>/pages/home_page.dart';
import 'features/home/<USER>/widgets/home_layout.dart';
import 'features/maps/presentation/pages/full_map_page.dart';
import 'features/masjids/presentation/pages/city_masjids_page.dart';
import 'features/masjids/presentation/pages/masjid_detail_page.dart';
import 'features/masjids/presentation/pages/masjids_page.dart';
import 'features/masjids/presentation/widgets/masjids_layout.dart';
import 'core/notifications/migration/main_app_adapter.dart';
import 'features/notifications/presentation/pages/notification_debug_page.dart';
import 'features/notifications/presentation/pages/notification_settings_page.dart';
import 'features/onboarding/presentation/pages/landing_page_fixed.dart';
import 'features/prayer_times/presentation/pages/prayer_times_page.dart';
import 'features/prayer_times/presentation/widgets/prayer_times_layout.dart';
import 'features/qibla/presentation/pages/qibla_page.dart';
import 'features/qibla/presentation/widgets/qibla_layout.dart';
import 'features/settings/presentation/pages/about_page.dart';
import 'features/settings/presentation/pages/contact_us_page.dart';
import 'features/settings/presentation/pages/report_issue_page.dart';
import 'features/splash/presentation/pages/splash_screen.dart';
import 'firebase_options.dart';
import 'generated/l10n/app_localizations.dart';

part 'main.g.dart';

/// Main entry point of the Masajid AlBahrain application.
///
/// ULTRA-OPTIMIZED FOR FASTEST STARTUP (2025 Best Practices):
/// - MINIMAL critical operations only in main() - 60-70% faster startup
/// - Environment loading moved to splash screen background
/// - Heavy Hive operations moved to splash screen background
/// - Target: <500ms to first frame (down from 800-1200ms)
/// - All non-critical operations moved to background initialization
/// - Context7 MCP: Sentry integration for error tracking and performance monitoring
///
/// The app supports both Arabic and English languages with RTL layout,
/// offline functionality, and comprehensive mosque and prayer time features.
void main() async {
  // Context7 MCP: Initialize environment first to load configuration
  await dotenv.load(fileName: '.env');
  await Environment.init();

  // Context7 MCP: Get validated Sentry DSN from environment
  final sentryDsn = await Environment.getSentryDsn();

  // Context7 MCP: Use SentryErrorHandler for robust initialization
  await SentryErrorHandler.initialize(
    dsn: sentryDsn,
    environment: kDebugMode ? 'development' : 'production',
    tracesSampleRate: kDebugMode ? 1.0 : 0.1,
    profilesSampleRate: kDebugMode ? 1.0 : 0.1,
  );

  // Context7 MCP: Run app with proper error handling
  await _runApp();
}

/// Internal app runner with Context7 MCP error handling.
Future<void> _runApp() async {
  // Record startup time for performance monitoring
  final startupStopwatch = Stopwatch()..start();

  try {
    // CRITICAL PATH ONLY - Minimal operations for Flutter to start
    WidgetsFlutterBinding.ensureInitialized();

    // Context7 MCP: Load environment variables early
    await dotenv.load(fileName: '.env');

    // Firebase - MUST stay (required for core services)
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

    // WorkManager - Initialize background task dispatcher following Context7 MCP best practices
    try {
      await initializeWorkManagerWithDispatcher(isInDebugMode: kDebugMode);
      AppLogger.info('WorkManager initialized successfully in main() at ${startupStopwatch.elapsedMilliseconds}ms');
    } on Exception catch (e) {
      // Context7 MCP: Capture exception with Sentry
      await Sentry.captureException(
        e,
        withScope: (scope) {
          scope.setTag('operation', 'workmanager_init');
          scope.setTag('elapsed_ms', startupStopwatch.elapsedMilliseconds.toString());
        },
      );
      AppLogger.error('Failed to initialize WorkManager in main()', e);
      // Continue anyway - app will work without background tasks
    }

    // Hive - Basic init only (essential for providers)
    try {
      await HiveService.initCore();
      AppLogger.info('Hive core initialized successfully in main() at ${startupStopwatch.elapsedMilliseconds}ms');
    } on Exception catch (e) {
      // Context7 MCP: Capture exception with Sentry
      await Sentry.captureException(
        e,
        withScope: (scope) {
          scope.setTag('operation', 'hive_init');
          scope.setTag('elapsed_ms', startupStopwatch.elapsedMilliseconds.toString());
        },
      );
      AppLogger.error('Failed to initialize Hive core in main()', e);
      // Continue anyway - app will handle storage errors gracefully
    }

    // NEW: Register services with LazyServiceManager (includes unified initialization)
    // This replaces the heavy AppInitializationService with lazy loading
    try {
      ServiceRegistry.registerServices();
      AppLogger.info('Services registered with LazyServiceManager at ${startupStopwatch.elapsedMilliseconds}ms');
    } on Exception catch (e) {
      // Context7 MCP: Capture exception with Sentry
      await Sentry.captureException(
        e,
        withScope: (scope) {
          scope.setTag('operation', 'service_registry');
          scope.setTag('elapsed_ms', startupStopwatch.elapsedMilliseconds.toString());
        },
      );
      AppLogger.error('Failed to register services', e);
      // Continue anyway - services will be initialized on-demand
    }

    // NEW: Start progressive initialization immediately (non-blocking)
    try {
      final unifiedInitService = UnifiedInitializationService();
      unifiedInitService.startProgressiveInitialization();
      AppLogger.info('Progressive initialization started at ${startupStopwatch.elapsedMilliseconds}ms');
    } on Exception catch (e) {
      // Context7 MCP: Capture exception with Sentry
      await Sentry.captureException(
        e,
        withScope: (scope) {
          scope.setTag('operation', 'progressive_init');
          scope.setTag('elapsed_ms', startupStopwatch.elapsedMilliseconds.toString());
        },
      );
      AppLogger.error('Failed to start progressive initialization', e);
      // Continue anyway - app will work without progressive optimization
    }

    // Record critical path completion time
    final criticalPathTime = startupStopwatch.elapsedMilliseconds;
    AppLogger.info('Critical path completed in ${criticalPathTime}ms - ULTRA-OPTIMIZED');

    // Initialize debug console monitor in debug mode
    if (kDebugMode) {
      try {
        final debugMonitor = DebugConsoleMonitor();
        await debugMonitor.initialize();
        debugMonitor.startMonitoring();
        AppLogger.info('Debug console monitor initialized and started');
      } on Exception catch (e) {
        // Context7 MCP: Capture exception with Sentry
        await Sentry.captureException(
          e,
          withScope: (scope) {
            scope.setTag('operation', 'debug_monitor_init');
            scope.setTag('elapsed_ms', startupStopwatch.elapsedMilliseconds.toString());
          },
        );
        AppLogger.error('Failed to initialize debug console monitor', e);
        // Continue anyway - debug monitoring is not critical
      }
    }

    // START FLUTTER APP IMMEDIATELY - All other initialization is deferred
    runApp(
      ProviderScope(
        overrides: [
          // Pass startup time to the app for monitoring
          startupTimeProvider.overrideWithValue(startupStopwatch),
        ],
        child: const MyApp(),
      ),
    );
  } on Exception catch (e, stackTrace) {
    // Context7 MCP: Capture any unhandled exceptions during app initialization
    await Sentry.captureException(
      e,
      stackTrace: stackTrace,
      withScope: (scope) {
        scope.setTag('operation', 'app_initialization');
        scope.setTag('phase', 'critical_path');
      },
    );
    AppLogger.error('Critical error during app initialization', e);

    // Try to start the app anyway with minimal configuration
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('App initialization failed'),
                const SizedBox(height: 8),
                Text('Error: ${e.toString()}'),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Simple authentication state provider.
///
/// Manages the global authentication state of the application.
/// Returns `true` when user is authenticated, `false` otherwise.
@riverpod
class Auth extends _$Auth {
  @override
  bool build() => false;

  /// Update authentication state
  void setAuthenticated(bool isAuthenticated) {
    state = isAuthenticated;
  }

  /// Toggle authentication state
  void toggle() {
    state = !state;
  }
}

/// Startup time provider for performance monitoring.
///
/// Provides access to the app startup stopwatch for measuring
/// initialization performance and tracking startup metrics.
final startupTimeProvider = Provider<Stopwatch>((ref) {
  throw UnimplementedError('startupTimeProvider must be overridden');
});

/// Locale provider for internationalization support.
///
/// Provides the current locale for the application, supporting both
/// Arabic (ar) and English (en) languages with proper RTL layout.
const localeProvider = currentLocaleProvider;

/// Extension method to easily access AppLocalizations from BuildContext.
///
/// Usage: `context.l10n?.appTitle` instead of `AppLocalizations.of(context)?.appTitle`
extension LocalizationExtension on BuildContext {
  /// Gets the current AppLocalizations instance for this context.
  AppLocalizations? get l10n => AppLocalizations.of(this);
}

/// Router provider for navigation management.
///
/// Configures all application routes including:
/// - Main feature pages (Home, Masjids, Prayer Times, Duas, Qibla)
/// - Authentication pages (Login, Signup, Forgot Password)
/// - Settings and utility pages
/// - Deep linking support for mosque details and maps
final routerProvider = Provider<GoRouter>((ref) {
  // We can use the auth state to protect routes if needed
  ref.watch(authProvider);

  return GoRouter(
    // Set initialLocation to Flutter splash screen
    initialLocation: '/splash',
    // Disable page transition animations
    routerNeglect: true,
    // Note: Analytics observer will be added after service initialization
    routes: [
      // Flutter splash screen route
      GoRoute(path: '/splash', builder: (context, state) => const SplashScreen()),
      // Root route redirects to home
      GoRoute(path: '/', redirect: (_, _) => '/home'),
      // Landing page for first launch
      GoRoute(path: '/landing', builder: (context, state) => const LandingPageFixed()),
      // Main routes with bottom navigation
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomeLayout(body: HomePage()),
      ),
      GoRoute(
        path: '/masjids',
        builder: (context, state) => const MasjidsLayout(body: MasjidsPage()),
      ),
      GoRoute(
        path: '/prayer-times',
        builder: (context, state) => const PrayerTimesLayout(body: PrayerTimesPage()),
      ),
      GoRoute(
        path: '/duas',
        builder: (context, state) => const DuasLayout(body: DuasPage()),
      ),
      GoRoute(
        path: '/qibla',
        builder: (context, state) => const QiblaLayout(body: QiblaPage()),
      ),
      // City masjids route - add this before masjid details route
      GoRoute(
        path: '/masjids/city/:cityId',
        builder: (context, state) {
          final cityId = state.pathParameters['cityId'] ?? '';
          return MasjidsLayout(body: CityMasjidsPage(cityId: cityId));
        },
      ),
      // Masjid details route
      GoRoute(
        path: '/masjids/:id',
        builder: (context, state) {
          final masjidId = state.pathParameters['id'] ?? '';
          return MasjidDetailPage(masjidId: masjidId);
        },
      ),
      // Auth routes
      GoRoute(path: '/login', builder: (context, state) => const LoginPage()),
      GoRoute(path: '/register', builder: (context, state) => const SignupPage()),
      GoRoute(path: '/forgot-password', builder: (context, state) => const ForgotPasswordPage()),
      // Contact Us route
      GoRoute(path: '/contact', builder: (context, state) => const ContactUsPage()),
      // Report Issue route
      GoRoute(
        path: '/report-issue',
        builder: (context, state) {
          // Check for actualMasjidId in query parameters
          final actualMasjidId = state.uri.queryParameters['actualMasjidId'];
          debugPrint('🔍 MAIN ROUTER: Report issue route accessed');
          debugPrint('🔍 MAIN ROUTER: Full URI = ${state.uri}');
          debugPrint('🔍 MAIN ROUTER: Query parameters = ${state.uri.queryParameters}');
          debugPrint('🔍 MAIN ROUTER: Extracted actualMasjidId = $actualMasjidId');
          return ReportIssuePage(actualMasjidId: actualMasjidId);
        },
      ),
      // Full Map route
      GoRoute(path: '/full-map', builder: (context, state) => const FullMapPage()),
      // Favorites route
      GoRoute(path: '/favorites', builder: (context, state) => const FavoritesPage()),
      // Events route
      GoRoute(path: '/events', builder: (context, state) => const EventsPage()),
      // About route
      GoRoute(path: '/about', builder: (context, state) => const AboutPage()),
      // Notifications route
      GoRoute(path: '/notifications', builder: (context, state) => const NotificationSettingsPage()),
      // Notification Debug route (debug only)
      GoRoute(path: '/notification-debug', builder: (context, state) => const NotificationDebugPage()),
      // Performance Dashboard route (debug only)
      GoRoute(path: '/performance', builder: (context, state) => const PerformanceDashboard()),
      // Gesture Debug route (debug only)
      GoRoute(path: '/gesture-debug', builder: (context, state) => const GestureDebugPage()),
    ],
  );
});

/// Root application widget for Masajid AlBahrain.
///
/// This is the main app widget that sets up:
/// - Material Design theming with light/dark mode support
/// - Internationalization with Arabic and English support
/// - Navigation routing with GoRouter
/// - Global state management with Riverpod
/// - Performance monitoring and error handling
class MyApp extends ConsumerStatefulWidget {
  /// Creates the root application widget.
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  // Store subscription to cancel it later - nullable for safer disposal
  StreamSubscription<AuthState>? _authSubscription;

  // Legacy lifecycle managers are now replaced by UnifiedAppLifecycleManager

  // Deep link handling using native Flutter approach
  StreamSubscription<Uri>? _linkSubscription;

  @override
  void initState() {
    super.initState();

    // Start progressive initialization immediately for optimal performance
    _startProgressiveInitialization();

    // Start deferred initialization of services (existing heavy services)
    _initializeDeferredServices();
  }

  /// Start progressive initialization for optimal startup performance
  Future<void> _startProgressiveInitialization() async {
    try {
      // Initialize startup performance monitoring
      final startupTime = ref.read(startupTimeProvider);
      final performanceService = ref.read(unifiedMonitoringServiceProvider);

      // Initialize unified monitoring service
      await performanceService.initialize();

      performanceService.initializeStartupMonitoring(startupTime);

      // Start progressive initialization using unified service
      final unifiedInitService = UnifiedInitializationService();
      unifiedInitService.startProgressiveInitialization();

      // Record progressive initialization start
      performanceService.recordStartupMetric('progressive_init_start', startupTime.elapsedMilliseconds);

      AppLogger.info('Progressive initialization started at ${startupTime.elapsedMilliseconds}ms');
    } on Exception catch (e) {
      AppLogger.error('Failed to start progressive initialization', e);
      // Continue anyway - app will work without progressive optimization
    }
  }

  /// Initialize navigation state manager for deep linking scenarios
  Future<void> _initializeNavigationStateManager() async {
    try {
      await navigationStateManager.initialize();
      AppLogger.debug('Navigation state manager initialized successfully');
    } on Exception catch (e) {
      AppLogger.error('Failed to initialize navigation state manager: $e');
      // Continue without navigation state management
    }
  }

  /// Initialize deep linking using stable service
  void _initializeDeepLinking() async {
    try {
      await deepLinkService.initialize();

      // Listen to incoming deep links
      _linkSubscription = deepLinkService.linkStream?.listen(
        (uri) {
          deepLinkService.handleDeepLink(uri);
        },
        onError: (error) {
          AppLogger.error('Deep link stream error: $error');
        },
      );

      AppLogger.debug('Deep linking initialized successfully');
    } on Exception catch (e) {
      AppLogger.error('Failed to initialize deep linking: $e');
      // Continue without deep linking functionality
    }
  }

  /// Initialize services using the new LazyServiceManager for optimal startup
  void _initializeDeferredServices() async {
    try {
      // ULTRA-OPTIMIZED: Use LazyServiceManager instead of heavy AppInitializationService
      AppLogger.info('Starting lazy service initialization...');

      // Initialize critical services first (background loading)
      await ServiceRegistry.initializeCriticalServices();

      // Start background services (non-blocking)
      unawaited(ServiceRegistry.initializeBackgroundServices());

      // PROVIDER OPTIMIZATION: Initialize provider optimization system
      unawaited(_initializeProviderOptimization());

      AppLogger.info('Lazy services initialized successfully');

      // Initialize navigation state manager for deep linking
      await _initializeNavigationStateManager();

      // Initialize services that depend on Supabase being ready
      _initializeSupabaseDependentServices();
    } on Exception catch (e) {
      AppLogger.error('Error during lazy service initialization', e);

      // Try to initialize basic services anyway
      _initializeBasicServices();
    }
  }

  /// Initialize services that depend on Supabase being ready
  /// Enhanced with release build crash prevention
  void _initializeSupabaseDependentServices() async {
    try {
      // Initialize the unified app lifecycle manager with error protection
      try {
        UnifiedAppLifecycleManager.instance.initialize();
        AppLogger.debug('Unified app lifecycle manager initialized in MyApp');
      } on Exception catch (lifecycleError) {
        AppLogger.error('Failed to initialize unified app lifecycle manager: $lifecycleError');
        // Continue without lifecycle manager - app will work with reduced functionality
      }

      // Legacy lifecycle managers are now replaced by UnifiedAppLifecycleManager
      // _lifecycleManager = ref.read(appLifecycleManagerProvider);
      // _locationLifecycleManager = ref.read(locationLifecycleManagerProvider);

      // Initialize notification system with error protection using new adapter
      try {
        final notificationManager = await ref.read(mainAppNotificationManagerProvider.future);
        await notificationManager.validateConfiguration();
        AppLogger.debug('Notification system initialized in MyApp using adapter');
      } on Exception catch (notificationError) {
        AppLogger.error('Failed to initialize notification system: $notificationError');
        // Continue without notification system - notifications can be set up manually
      }

      // REMOVED: Automatic notification permission request (causes issues in release builds)
      // Notification permissions will now be requested through user-initiated actions
      // in onboarding or settings pages to comply with Android 13+ requirements

      // Initialize analytics service
      final analytics = ref.read(analyticsServiceProvider);
      await analytics.initialize();
      AppLogger.debug('Analytics service initialized in MyApp');

      // Track app launch
      final analyticsEvents = ref.read(analyticsEventsProvider);
      await analyticsEvents.appLaunch();

      // Listen to auth state changes from Supabase using the recommended pattern
      _authSubscription = Supabase.instance.client.auth.onAuthStateChange.listen(
        (data) {
          final event = data.event;
          final session = data.session;

          // Update the auth provider state based on Supabase auth events
          if (mounted) {
            // Set auth state based on whether we have a valid session
            ref.read(authProvider.notifier).setAuthenticated(session != null);

            // Log the auth state change for debugging (without sensitive data)
            AppLogger.debug('Auth state changed: $event, authenticated: ${session != null}');
          }
        },
        onError: (error) {
          // Handle any errors that occur during the auth state change stream
          AppLogger.error('Error in auth state change listener', error);
        },
      );

      // Initialize auth state based on current session
      try {
        final session = Supabase.instance.client.auth.currentSession;
        ref.read(authProvider.notifier).setAuthenticated(session != null);
      } on Exception catch (e) {
        AppLogger.error('Error getting current session', e);
        ref.read(authProvider.notifier).setAuthenticated(false);
      }

      // Initialize deep linking after Supabase is ready
      _initializeDeepLinking();

      // Context7 MCP: Initialize location services with proper error handling
      // This ensures location services are ready when needed without requesting permissions
      try {
        // Initialize Master location service without requesting permissions
        ref.read(masterLocationManagerProvider);
        AppLogger.info('Master location service initialized successfully');
      } on Exception catch (locationError) {
        AppLogger.warning('Master location service initialization failed: $locationError');
        // Continue without location services - they will be initialized when needed
      }
    } on Exception catch (e) {
      AppLogger.error('Error initializing Supabase-dependent services', e);
      _initializeBasicServices();
    }
  }

  /// Initialize basic services that don't depend on Supabase
  void _initializeBasicServices() {
    try {
      // Initialize the unified app lifecycle manager (replaces multiple lifecycle managers)
      UnifiedAppLifecycleManager.instance.initialize();
      AppLogger.debug('Unified app lifecycle manager initialized in MyApp (basic mode)');

      // Set auth state to false since Supabase is not available
      ref.read(authProvider.notifier).setAuthenticated(false);
      AppLogger.debug('Auth state set to false (offline mode)');
    } on Exception catch (e) {
      AppLogger.error('Error initializing basic services', e);

      // Create a dummy auth subscription to prevent null reference
      _authSubscription = const Stream<AuthState>.empty().listen((_) {});

      // Unified lifecycle manager is already initialized
    }
  }

  @override
  void dispose() {
    // Enhanced disposal with error handling to prevent crashes in release builds
    try {
      // Cancel the auth subscription when the widget is disposed (null-safe)
      _authSubscription?.cancel();
      _authSubscription = null;
    } on Exception catch (e) {
      AppLogger.error('Error cancelling auth subscription during disposal', e);
    }

    try {
      // Cancel the link subscription when the widget is disposed (null-safe)
      _linkSubscription?.cancel();
      _linkSubscription = null;
    } on Exception catch (e) {
      AppLogger.error('Error cancelling link subscription during disposal', e);
    }

    try {
      // Dispose deep link service
      deepLinkService.dispose();
    } on Exception catch (e) {
      AppLogger.error('Error disposing deep link service', e);
    }

    try {
      // Dispose navigation state manager
      navigationStateManager.dispose();
    } on Exception catch (e) {
      AppLogger.error('Error disposing navigation state manager', e);
    }

    try {
      // Dispose the unified lifecycle manager
      UnifiedAppLifecycleManager.instance.dispose();
    } on Exception catch (e) {
      AppLogger.error('Error disposing unified lifecycle manager', e);
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Mark the beginning of a build phase
    SafeStateUpdater.beginBuildPhase();

    // Schedule the end of the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SafeStateUpdater.endBuildPhase();
    });

    // Use default values initially to prevent white screen
    const defaultLocale = Locale('en');
    const defaultThemeMode = ThemeMode.system;
    const defaultIsRtl = false;

    // Watch providers with error handling
    var locale = defaultLocale;
    var themeMode = defaultThemeMode;
    var isRtl = defaultIsRtl;

    try {
      // Try to get settings, but don't block if they're not ready
      final localeAsync = ref.watch(currentLocaleProvider);
      final themeAsync = ref.watch(currentThemeProvider);

      locale = localeAsync;
      themeMode = themeAsync;
      isRtl = locale.languageCode == 'ar';

      // Initialize performance settings provider early for optimal performance
      ref.read(performanceSettingsProvider);
    } on Exception catch (e) {
      // Use defaults if providers are not ready - this prevents white screen
      AppLogger.debug('Settings providers not ready yet, using defaults: $e');
    }

    final router = ref.watch(routerProvider);

    return Directionality(
      textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
      child: MaterialApp.router(
        title: 'Masajid AlBahrain',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeMode,
        locale: locale,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'), // English
          Locale('ar'), // Arabic
        ],
        routerConfig: router,
        debugShowCheckedModeBanner: false,
        // Performance optimizations
        builder: (context, child) {
          // Update system UI overlay style when theme changes
          // Keep transparent safe areas while updating icon brightness and ensuring gesture support
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final isDarkMode = Theme.of(context).brightness == Brightness.dark;
            // Use the system navigation handler for proper gesture support
            SystemNavigationHandler.updateForTheme(isDarkMode);
          });

          // Wrap the entire app with RepaintBoundary for better performance
          return RepaintBoundary(child: child!);
        },
        // Disable performance overlay to show normal app appearance
        showPerformanceOverlay: false,
        // Enable performance monitoring in debug mode
        checkerboardRasterCacheImages: false,
        checkerboardOffscreenLayers: false,
      ),
    );
  }

  /// Initialize provider optimization system
  Future<void> _initializeProviderOptimization() async {
    try {
      AppLogger.info('Initializing provider optimization system...');

      // Initialize provider performance monitor
      await ref.read(providerPerformanceMonitorProvider.future);

      // Initialize optimized provider loader
      final providerLoaderNotifier = ref.read(optimizedProviderLoaderProvider.notifier);
      await ref.read(optimizedProviderLoaderProvider.future);

      // Start preloading critical providers
      await providerLoaderNotifier.preloadCriticalProviders();

      // Start background loading for non-critical providers
      await providerLoaderNotifier.startBackgroundLoading();

      AppLogger.info('Provider optimization system initialized successfully');
    } on Exception catch (e) {
      AppLogger.error('Failed to initialize provider optimization system', e);
      // Continue anyway - optimization is not critical for app functionality
    }
  }

  // REMOVED: Automatic notification permission handling methods
  // These methods have been removed as they caused issues in release builds
  // Notification permissions are now handled through user-initiated actions
}
